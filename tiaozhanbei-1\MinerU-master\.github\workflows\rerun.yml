name: check-status

on:
  workflow_run:
    workflows: [ci]
    types: [completed]

jobs:
  on-failure:
    runs-on: pdf
    permissions:
      actions: write
    if: ${{ (github.event.workflow_run.head_branch == 'master') && github.event.workflow_run.conclusion == 'failure' && github.event.workflow_run.run_attempt < 3 }}
    steps:
      - run: |
          echo 'The triggering workflow failed'
          sleep 600
          curl -L \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${{ github.token }}" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          https://api.github.com/repos/${{ github.repository }}/actions/runs/${{ github.event.workflow_run.id }}/rerun-failed-jobs 
