import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import lightgbm as lgb
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import train_test_split, TimeSeriesSplit, RandomizedSearchCV
from sklearn.multioutput import MultiOutputRegressor
import warnings
import os
import joblib
from datetime import datetime
import re
import time
from scipy.stats import randint, uniform
import matplotlib.dates as mdates
import os
import re
import openpyxl  # 添加openpyxl库用于Excel操作
# 为了确保找到WaterLevelPredictor模块
import sys

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 将当前目录添加到系统路径
sys.path.append(current_dir)
from WaterLevelPredictor import WaterLevelPredictor


def main():
    print("=" * 80)
    print("南水北调中线节制闸水位预测")
    print("=" * 80)

    # 初始化预测器
    model_dir = r"D:\python project2\diaohe\lightgbm_models"
    predictor = WaterLevelPredictor(model_dir=model_dir)

    while True:
        print("\n请选择预测模式:")
        print("1. Excel文件批量预测")
        print("2. 实时数据单点预测")
        print("3. 退出")
        choice = input("请输入选项 (1-3): ")

        if choice == '1':
            file_path = input("请输入Excel文件路径: ").strip('"')
            save_path = input("请输入结果保存路径：").strip('"')

            if not os.path.exists(file_path):
                print("文件不存在，请检查路径")
                continue

            try:
                print("开始处理Excel文件并预测...")
                # 执行预测
                prediction_df = predictor.predict(file_path)

                # 确保保存路径以.xlsx结尾
                if not save_path.endswith('.xlsx'):
                    save_path += '.xlsx'

                print(f"预测完成，准备保存结果到: {save_path}")

                try:
                    # 保存结果
                    directory = os.path.dirname(save_path)
                    if directory and not os.path.exists(directory):
                        os.makedirs(directory)

                    with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                        # 写入预测结果
                        prediction_df.to_excel(writer, sheet_name='水位预测结果', index=True)

                        # 添加元数据页
                        metadata = pd.DataFrame({
                            '预测信息': [
                                f'预测时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                                f'模型版本: LightGBM多目标预测',
                                f'节制闸数量: {len(predictor.gate_names)}',
                                f'预测时间步长: {len(prediction_df)}'
                            ]
                        })
                        metadata.to_excel(writer, sheet_name='预测信息', index=False)

                        # 调整列宽
                        workbook = writer.book
                        worksheet = writer.sheets['水位预测结果']

                        # 设置时间列宽
                        worksheet.column_dimensions['A'].width = 20
                        worksheet['A1'] = '时间'

                        # 设置水位列为2位小数
                        for col_idx in range(1, len(prediction_df.columns) + 1):
                            col_letter = openpyxl.utils.get_column_letter(col_idx + 1)  # 跳过时间列
                            worksheet.column_dimensions[col_letter].width = 20

                            # 水位列设置数值格式为2位小数
                            for row in range(2, len(prediction_df) + 2):
                                cell = worksheet.cell(row=row, column=col_idx + 1)
                                cell.number_format = '0.00'

                    print(f"预测结果已成功保存至: {save_path}")

                except PermissionError:
                    print(f"文件已被打开，无法保存: {save_path}")
                    print("请关闭文件后重试")
                except Exception as e:
                    print(f"保存结果时出错: {e}")

            except Exception as e:
                print(f"预测失败: {e}")

        elif choice == '2':
            try:
                hours = int(input("请输入历史数据小时数 (至少24小时): "))
                hours = max(hours, 24)

                # 获取需要的列名
                columns = predictor.preprocessor.original_columns
                print(f"请输入 {hours} 小时的历史数据：")
                historical_data = []

                # 模拟24小时数据输入
                print("\n注意: 请按顺序输入以下数据, 时间可以自动生成:")
                print(f"数据列: {', '.join(columns)}")

                for i in range(hours):
                    print(f"\n[第 {i + 1} 小时数据]")
                    row_data = {}

                    for idx, col in enumerate(columns):
                        # 特殊处理时间列
                        if '时间' in col or 'time' in col.lower():
                            # 自动生成时间
                            time_value = datetime.now() - pd.DateOffset(hours=(hours - i - 1))
                            row_data[col] = time_value
                            print(f"时间自动生成: {time_value}")
                        else:
                            # 其他列输入数值
                            prompt = f"{col} [{predictor.standardize_column_name(col, '')}]: "
                            user_input = input(prompt) or "0.0"
                            row_data[col] = float(user_input)

                    historical_data.append(row_data)

                # 创建DataFrame
                df = pd.DataFrame(historical_data)

                # 提取时间列作为索引
                time_col = [col for col in df.columns if '时间' in col or 'time' in col.lower()]
                if not time_col:
                    print("未找到时间列，自动创建...")
                    time_index = [datetime.now() - pd.DateOffset(hours=(hours - i - 1)) for i in range(hours)]
                    df.index = time_index
                else:
                    time_col = time_col[0]
                    df = df.set_index(time_col)

                # 执行预测
                prediction_df = predictor.predict(df)

                # 输出最近的预测结果
                print("\n最近一小时的水位预测结果:")
                print("=" * 70)
                for gate, level in prediction_df.iloc[-1].items():
                    gate_name = " - ".join(gate.split("_")[:2])
                    print(f"{gate_name}: {level:.3f} m")
                print("=" * 70)

                # 询问是否保存结果
                save_choice = input("是否保存结果？(y/n): ").strip().lower()
                if save_choice == 'y':
                    save_path = input("请输入保存路径：").strip('"')
                    if not save_path.endswith('.xlsx'):
                        save_path += '.xlsx'

                    try:
                        # 保存结果
                        directory = os.path.dirname(save_path)
                        if directory and not os.path.exists(directory):
                            os.makedirs(directory)

                        with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                            # 写入预测结果
                            prediction_df.to_excel(writer, sheet_name='水位预测结果', index=True)

                            # 添加元数据页
                            metadata = pd.DataFrame({
                                '预测信息': [
                                    f'预测时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                                    f'模型版本: LightGBM多目标预测',
                                    f'预测时间步长: {len(prediction_df)}'
                                ]
                            })
                            metadata.to_excel(writer, sheet_name='预测信息', index=False)

                            # 设置样式/格式
                            workbook = writer.book
                            worksheet = writer.sheets['水位预测结果']

                            # 设置时间列宽
                            worksheet.column_dimensions['A'].width = 20
                            worksheet['A1'] = '时间'

                            # 设置水位列
                            for col_idx in range(1, len(prediction_df.columns) + 1):
                                col_letter = openpyxl.utils.get_column_letter(col_idx + 1)
                                worksheet.column_dimensions[col_letter].width = 20

                                # 设置数值格式
                                for row in range(2, len(prediction_df) + 2):
                                    cell = worksheet.cell(row=row, column=col_idx + 1)
                                    cell.number_format = '0.00'

                        print(f"预测结果已成功保存至: {save_path}")
                    except Exception as e:
                        print(f"保存结果时出错: {e}")
                else:
                    print("未保存结果")

            except Exception as e:
                print(f"单点预测失败: {e}")

        elif choice == '3':
            print("退出预测系统")
            break

        else:
            print("无效选项，请重新输入")


if __name__ == "__main__":
    main()
