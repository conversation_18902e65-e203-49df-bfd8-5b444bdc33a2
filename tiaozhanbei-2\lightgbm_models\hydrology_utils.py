import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import lightgbm as lgb
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import train_test_split, TimeSeriesSplit, RandomizedSearchCV
from sklearn.multioutput import MultiOutputRegressor
import warnings
import os
import joblib
from datetime import datetime
import re
import shap
import time
from scipy.stats import randint, uniform
import matplotlib.dates as mdates

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set(style='whitegrid', palette='muted', font='SimHei')


class AdvancedHydrologyPreprocessor:
    def __init__(self, target_columns):
        self.target_columns = target_columns
        self.scalers = {}
        self.feature_names = []
        self.original_columns = []  # 保存原始列名

    def create_sequences(self, data, sequence_length=24):
        """创建时间序列序列 - 为树模型构建特征"""
        missing_targets = [col for col in self.target_columns if col not in data.columns] #返回不在data中但在目标列中列，即缺失列
        if missing_targets:
            raise ValueError(f"目标列 {missing_targets} 不存在于数据中")

        feature_cols = [col for col in data.columns if col not in self.target_columns] #返回不在目标列但在data中的列，即特征列
        X, y = [], []

        # 构建扩展特征集，包含滞后特征和滚动统计
        for i in range(sequence_length, len(data)):
            # 当前时刻的特征
            current_features = []

            # 添加滞后特征
            for lag in range(1, sequence_length + 1): #滞后于i的24小时的数据，此循环结束后，current_features添加24行滞后数据
                lag_index = i - sequence_length + lag
                lag_features = data[feature_cols].iloc[lag_index] #每个i时刻特征列的滞后数据（1行滞后数据）
                for col in feature_cols:
                    current_features.append(lag_features[col]) #逐个加入到current_features中
                    # 添加列名后缀
                    self.feature_names.append(f"{col}_lag{sequence_length - lag + 1}") #对应的加入滞后名

            # 添加滚动统计特征
            window = data[feature_cols].iloc[i - sequence_length:i] #和之前数据形式一样，不滞后
            rolling_mean = window.mean()
            rolling_std = window.std()
            for col in feature_cols:
                current_features.append(rolling_mean[col])
                current_features.append(rolling_std[col])
                self.feature_names.append(f"{col}_roll_mean")
                self.feature_names.append(f"{col}_roll_std")

            # 添加时间特征
            row_time = data.index[i]
            current_features.append(row_time.hour)
            current_features.append(row_time.dayofweek)
            current_features.append(row_time.month)
            current_features.append(np.sin(2 * np.pi * row_time.hour / 24))
            current_features.append(np.cos(2 * np.pi * row_time.hour / 24))
            self.feature_names.extend(['hour', 'day_of_week', 'month', 'hour_sin', 'hour_cos'])

            X.append(current_features)
            y.append(data[self.target_columns].iloc[i].values)

        # 去除重复的特征名
        self.feature_names = list(dict.fromkeys(self.feature_names))
        return np.array(X), np.array(y), self.feature_names

    def fit_transform(self, data):
        """拟合并转换数据 - 多目标版本"""
        scaled_data = data.copy()
        self.scalers = {}


        # 先处理所有开度列
        for col in list(data.columns):
            if '开度' in col:
                # 拆分并替换原始列
                split_columns = self.process_and_split_opening_data(data[col])

                # 添加拆分后的新列
                for i, new_col in enumerate(split_columns):
                    new_col_name = f"{col}_{i + 1}"
                    if new_col_name not in scaled_data.columns:  # 避免重复添加
                        scaled_data[new_col_name] = new_col

                # 移除原始开度列
                scaled_data.drop(col, axis=1, inplace=True)

        # 保存清理后的列名
        cleaned_columns = scaled_data.columns.tolist()
        self.original_columns = cleaned_columns  # 保存原始列名

        # 然后对所有数值列进行标准化
        for col in cleaned_columns:
            if not pd.api.types.is_numeric_dtype(scaled_data[col]):
                continue

            scaler = RobustScaler()
            scaled_values = scaler.fit_transform(scaled_data[[col]])
            scaled_data[col] = scaled_values.flatten()  # 直接赋值扁平化数组
            self.scalers[col] = scaler

        # 检查NaN或Inf
        nan_count = np.count_nonzero(np.isnan(scaled_data))
        inf_count = np.count_nonzero(np.isinf(scaled_data))

        try:
            if nan_count > 0 or inf_count > 0:
                nan_cols = scaled_data.columns[np.isnan(scaled_data).any(axis=0)]
                raise ValueError(f"预处理后存在NaN: {nan_count}, Inf: {inf_count}，问题列: {list(nan_cols)}")

        except ValueError as e:
            # 可以在这里做数据的修复，或者记录日志并继续处理
            print(f"捕获到错误: {e}")

        return scaled_data

    def process_and_split_opening_data(self, opening_series):
        """处理开度系列数据，拆分为多个数值列"""

        # 处理整个Series而不是单个值
        def split_values(value):
            if isinstance(value, str):
                # 处理多种分隔符：空格、斜杠、逗号等
                parts = re.split(r'[/,_\s]+', value.strip())
                return [float(part) if part.replace('.', '', 1).isdigit() else np.nan
                        for part in parts if part]
            elif pd.isna(value):
                return [np.nan]
            else:
                return [float(value)]

        # 拆分所有值并确定最大列数
        split_results = opening_series.apply(split_values)
        max_columns = max(len(x) for x in split_results)

        # 创建拆分后的列
        split_columns = []
        for i in range(max_columns):
            new_col = split_results.apply(
                lambda x: x[i] if i < len(x) else np.nan
            )
            split_columns.append(new_col)

        return split_columns

    def transform(self, data):
        """转换新数据"""
        scaled_data = data.copy()
        for col in data.columns:
            if col in self.scalers:
                scaled_data[col] = self.scalers[col].transform(data[[col]]).flatten()
        return scaled_data

    def inverse_transform_targets(self, scaled_values):
        """反转换多目标值"""
        inverted = np.zeros_like(scaled_values)
        for i, col in enumerate(self.target_columns):
            if col in self.scalers:
                inverted[:, i] = self.scalers[col].inverse_transform(scaled_values[:, i].reshape(-1, 1)).flatten()
        return inverted
