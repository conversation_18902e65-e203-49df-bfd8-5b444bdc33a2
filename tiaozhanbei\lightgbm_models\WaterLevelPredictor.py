import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import lightgbm as lgb
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import train_test_split, TimeSeriesSplit, RandomizedSearchCV
from sklearn.multioutput import MultiOutputRegressor
import warnings
import os
import joblib
from datetime import datetime
import re
import time
from scipy.stats import randint, uniform
import matplotlib.dates as mdates
import os
import re


class WaterLevelPredictor:
    def __init__(self, model_dir="D:\python project2\diaohe\lightgbm_models"):
        """
        参数:
            model_dir: 包含模型文件的目录路径
        """
        # 加载所需组件
        self.model = joblib.load(os.path.join(model_dir, "lightgbm_model.pkl"))
        self.preprocessor = joblib.load(os.path.join(model_dir, "preprocessor.pkl"))
        self.feature_names = joblib.load(os.path.join(model_dir, "features.pkl"))
        self.original_columns = self.preprocessor.original_columns

        # 从元数据获取关键参数
        self.gate_names = [
            '陶岔渠首引水闸',
            '刁河渡槽进口节制闸',
            '湍河渡槽进口节制闸',
            '严陵河渡槽进口节制闸',
            '镇平淇河倒虹吸出口节制闸',
            '十二里河渡槽进口节制闸'
        ]
        self.sequence_length = 24
        self.result_df = []
        self.target_water_levels = []
        for gate in self.gate_names:
            self.target_water_levels.append(f"{gate}_闸前水位(m)")
            self.target_water_levels.append(f"{gate}_闸后水位(m)")

        # 为预处理器注入关键字列表
        self.flow_keywords = ['流量', 'flow', '引水']
        self.gate_keywords = ['开度', 'opening', '闸门']
        self.water_level_keywords = ['水位', 'level']
        self.design_water_level_diff_keywords = ['设计水位差', '水位差', '水位差值']
        self.exclude_keywords = ['时间', 'time', 'unnamed', '备注']

        self.preprocessor.flow_keywords = self.flow_keywords
        self.preprocessor.gate_keywords = self.gate_keywords
        self.preprocessor.water_level_keywords = self.water_level_keywords
        self.preprocessor.exclude_keywords = self.exclude_keywords

    def standardize_column_name(self, col, gate_name):

        col_lower = col.lower().replace(' ', '').replace('\t', '')

        # 设计水位差相关的关键字
        design_water_level_diff_keywords = ['设计水位差', '水位差', '水位差值']
        if any(k in col_lower for k in design_water_level_diff_keywords):
            return f"{gate_name}_{col}"

        patterns = {
            tuple(self.water_level_keywords): '水位(m)',
            tuple(self.flow_keywords): '流量(m³/s)',
            tuple(self.gate_keywords): '开度(%)'
        }

        # 遍历模式匹配
        for keywords, suffix in patterns.items():
            if any(k in col_lower for k in keywords):
                if '闸前' in col_lower:
                    return f"{gate_name}_闸前{suffix}"
                elif '闸后' in col_lower:
                    return f"{gate_name}_闸后{suffix}"
                else:
                    return f"{gate_name}_{col}"

        return f"{gate_name}_{col}"
    def parse_datetime_flexible(self,time_str):
        """灵活解析多种时间格式"""
        if pd.isna(time_str):
            return None

        time_str = str(time_str).strip()
        formats = [
            '%Y/%m/%d %H:%M',
            '%Y-%m-%d %H:%M',
            '%Y/%m/%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d',
            '%Y-%m-%d',
            '%Y/%m/%d %H',
            '%Y-%m-%d %H',
        ]

        for fmt in formats:
            try:
                return pd.to_datetime(time_str, format=fmt)
            except:
                continue

        try:
            return pd.to_datetime(time_str, infer_datetime_format=True)
        except:
            return None

    def process_and_split_opening_data(self, opening_series):
        """处理开度系列数据，拆分为多个数值列"""

        # 处理整个Series而不是单个值
        def split_values(value):
            if isinstance(value, str):
                # 处理多种分隔符：空格、斜杠、逗号等
                parts = re.split(r'[/,_\s]+', value.strip())
                return [float(part) if part.replace('.', '', 1).isdigit() else np.nan
                        for part in parts if part]
            elif pd.isna(value):
                return [np.nan]
            else:
                return [float(value)]

        # 拆分所有值并确定最大列数
        split_results = opening_series.apply(split_values)
        max_columns = max(len(x) for x in split_results)

        # 创建拆分后的列
        split_columns = []
        for i in range(max_columns):
            new_col = split_results.apply(
                lambda x: x[i] if i < len(x) else np.nan
            )
            split_columns.append(new_col)

        return split_columns

    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清理预测数据"""
        print("开始数据清理...")
        c_data = data.copy()
        # 先处理所有开度列
        print("开度列处理：")
        for col in list(data.columns):
            if '开度' in col:
                # 拆分并替换原始列
                split_columns = self.process_and_split_opening_data(data[col])

                # 添加拆分后的新列
                for i, new_col in enumerate(split_columns):
                    new_col_name = f"{col}_{i + 1}"
                    if new_col_name not in c_data.columns:  # 避免重复添加
                        c_data[new_col_name] = new_col

                # 移除原始开度列
                c_data.drop(col, axis=1, inplace=True)

        # 保存清理后的列名
        cleaned_columns = c_data.columns.tolist()
        self.original_columns = cleaned_columns  # 保存原始列名
        print("开度处理后的数据：",c_data.columns)

        # 处理异常值 - 只对特征列处理，目标列无需处理
        feature_cols = [col for col in c_data.columns if col not in self.target_water_levels]
        print(f"开始处理{len(feature_cols)}个特征列的异常值...")

        # 移除全为空的列，忽略目标列（假设目标列为空）
        c_data[feature_cols] = c_data[feature_cols].dropna(axis=1, how='all')

        # 移除重复列
        c_data[feature_cols] = c_data[feature_cols].loc[:, ~c_data[feature_cols].columns.duplicated()]
        print(f"清理重复列后: {c_data.shape}")

        for col in feature_cols:
            if c_data[col].nunique() < 10:
                continue  # 跳过分类列

            Q1 = c_data[col].quantile(0.05)
            Q3 = c_data[col].quantile(0.95)
            IQR = Q3 - Q1

            if IQR > 0:
                lower_bound = Q1 - 3 * IQR
                upper_bound = Q3 + 3 * IQR

                # 限制异常值
                c_data[col] = c_data[col].clip(lower_bound, upper_bound)
                n_outliers = len(c_data[(data[col] < lower_bound) | (c_data[col] > upper_bound)])

                if n_outliers > 0:
                    print(f"  {col} - 修正异常值: {n_outliers}个 ({n_outliers / len(data):.2%})")

        # 填充缺失值 - 只对特征列进行填充
        print(f"填充缺失值... (原有缺失值: {c_data.isnull().sum().sum()})")

        for col in c_data.columns:
            if col not in self.target_water_levels:  # 忽略目标列
                if c_data[col].isnull().any():
                    if c_data[col].isnull().sum() < 0.2 * len(data):  # 缺失值小于20%
                        c_data[col] = c_data[col].fillna(method='ffill')
                        c_data[col] = c_data[col].fillna(method='bfill')
                    else:
                        if c_data[col].dtype.kind in 'biufc':
                            mean_val = c_data[col].mean()
                            c_data[col] = c_data[col].fillna(mean_val)
                        else:
                            mode_val = c_data[col].mode()[0] if not c_data[col].mode().empty else ''
                            c_data[col] = c_data[col].fillna(mode_val)

        print(f"填充后缺失值: {c_data.isnull().sum().sum()}")

        # 如果仍有缺失值，删除剩余的行
        if c_data[feature_cols].isnull().sum().sum() > 0:
            c_data[feature_cols] = c_data[feature_cols].dropna()
            print(f"删除含缺失值的行后: {c_data.shape}")

        # 添加时间特征
        c_data['hour'] = c_data.index.hour
        c_data['day_of_week'] = c_data.index.dayofweek
        c_data['day_of_month'] = c_data.index.day
        c_data['month'] = c_data.index.month
        c_data['year'] = c_data.index.year

        # 周期性特征
        c_data['hour_sin'] = np.sin(2 * np.pi * c_data.index.hour / 24)
        c_data['hour_cos'] = np.cos(2 * np.pi * c_data.index.hour / 24)
        c_data['month_sin'] = np.sin(2 * np.pi * c_data.index.month / 12)
        c_data['month_cos'] = np.cos(2 * np.pi * c_data.index.month / 12)

        return c_data

    def load_and_prepare_input(self, input_path, input_gate_names=None, require_sequence_length=None):
        """
        从Excel加载输入数据并处理，确保有足够的数据点
        参数:
            input_path: 输入Excel文件路径
            input_gate_names: 指定要加载的节制闸列表
            require_sequence_length: 要求的最小数据长度（默认使用self.sequence_length）
        返回:
            处理后的DataFrame
        """
        # 默认使用模型设置的序列长度
        require_sequence_length = 50
        require_length = require_sequence_length if require_sequence_length else self.sequence_length

        print(f"从Excel文件加载输入数据: {input_path}")
        xl = pd.ExcelFile(input_path, engine='openpyxl')
        sheet_names = xl.sheet_names

        # 确定要加载的节制闸列表
        if input_gate_names is None:
            input_gate_names = [name for name in self.gate_names if name in sheet_names]
            print(f"自动检测到有效节制闸: {input_gate_names}")
        else:
            input_gate_names = [name for name in input_gate_names if name in sheet_names]
            print(f"使用指定节制闸: {input_gate_names}")

        if not input_gate_names:
            raise ValueError("未找到与模型匹配的节制闸数据")

        merged_data = None

        # 处理每个节制闸的数据
        for gate in input_gate_names:
            if gate not in sheet_names:
                print(f"警告: 未找到节制闸 '{gate}' 的数据Sheet")
                continue

            print(f"\n处理节制闸: {gate}")
            df = xl.parse(sheet_name=gate)

            # 查找时间列
            time_cols = [col for col in df.columns if any(k in col.lower() for k in ['时间', 'time', '日期', 'date'])]
            time_col = time_cols[0] if time_cols else df.columns[0]

            # 解析时间列
            try:
                df[time_col] = df[time_col].apply(self.parse_datetime_flexible)
                df = df.dropna(subset=[time_col])
                df = df.set_index(time_col)
                df = df.sort_index()
                print(f"  时间解析成功: 从 {df.index.min()} 到 {df.index.max()}, 共{len(df)}条记录")
            except Exception as e:
                print(f"  时间解析失败: {e}")
                continue

            # 重命名列并添加闸门前缀
            renamed_columns = {col: self.standardize_column_name(col, gate) for col in df.columns}
            df = df.rename(columns=renamed_columns)

            # 选择相关列
            relevant_columns = [col for col in df.columns if
                                not any(excl in col.lower() for excl in self.exclude_keywords)]
            df_selected = df[relevant_columns]

            # 添加到合并数据
            if merged_data is None:
                merged_data = df_selected
            else:
                try:
                    merged_data = merged_data.join(df_selected, how='outer')
                except:
                    print("合并数据时出错，改为concat操作")
                    merged_data = pd.concat([merged_data, df_selected], axis=1)

        if merged_data is None:
            raise ValueError("合并后数据为空")

            # === 修复: 使用最后一行数据直接复制 ===
        print(f"原始数据长度为: {len(merged_data)} (需要{require_length}条)")

        if len(merged_data) < require_length:
            print(f"数据长度不足{require_length}，进行时间序列填充...")

            # 获取最后一个时间点
            last_time = merged_data.index[-1]

            # 计算需要向前延伸的时间点
            # 创建新索引（24小时，包含原始数据的时间点）
            start_time = last_time - pd.Timedelta(hours=require_length - 1)
            new_index = pd.date_range(start=start_time, periods=require_length, freq='H')

            # 用最后一行数据填充新DataFrame
            filled_data = pd.DataFrame(
                data=[merged_data.iloc[-1].values] * require_length,  # 直接复制最后一行24次
                index=new_index,
                columns=merged_data.columns
            )

            # 直接使用填充后的数据（不再拼接）
            merged_data = filled_data

            print(f"填充后数据形状: {len(merged_data)}")
            print(f"新时间范围: {merged_data.index.min()} 到 {merged_data.index.max()}")

        # 检查目标列是否存在
        available_targets = [col for col in self.target_water_levels if col in merged_data.columns]
        if not available_targets:
            print("未找到目标水位列，显示所有可用列:")
            for col in merged_data.columns:
                if any(k in col for k in self.water_level_keywords):
                    print(f"  可用水位列: {col}")
            raise ValueError("未找到目标水位列")

        print(f"找到目标水位列: {available_targets}")

        # 确保包含所有目标列
        for target in self.target_water_levels:
            if target not in merged_data.columns:
                print(f"添加缺失目标列: {target}")
                merged_data[target] = np.nan
        print(f"清理前数据形状：{merged_data.shape}")

        # 添加时间特征
        merged_data = self.clean_data(merged_data)
        print(f"最终数据形状: {merged_data.shape}")

        return merged_data

    def prepare_prediction_input(self, input_data, require_sequence_length=None):
        """
        参数:
            input_data: 原始列名数据（可以是Excel路径或DataFrame）
            require_sequence_length: 需要的序列长度，默认使用self.sequence_length
        返回:
            预处理后的数据序列
        """
        req_length = require_sequence_length if require_sequence_length else self.sequence_length

        if isinstance(input_data, str) and input_data.endswith(('.xlsx', '.xls')):
            # 从Excel文件加载数据（自动处理长度不足的情况）
            print(f"从excel加载文件：")
            processed_data = self.load_and_prepare_input(input_data, require_sequence_length=req_length)
        elif isinstance(input_data, pd.DataFrame):
            # 直接处理DataFrame
            processed_data = input_data.copy()

            # 检查是否需要填充数据
            if len(processed_data) < req_length:
                print(f"输入数据长度({len(processed_data)})不足{req_length}小时，进行复制填充")

                # 关键修复：创建连续时间序列
                last_time = processed_data.index[-1]  # 最后一个时间点

                # 计算需要向前延伸的时间点（复制前24小时的数据）
                start_time = last_time - pd.Timedelta(hours=req_length - 1)
                new_index = pd.date_range(start=start_time,
                                          periods=req_length,
                                          freq='H')  # 小时频率

                # 创建新的数据集（时间向前延伸）
                filled_data = pd.DataFrame(columns=processed_data.columns, index=new_index)

                # 用最后一行数据填充全部行（但保留正确的时间索引）
                for col in processed_data.columns:
                    filled_data[col] = processed_data[col].iloc[-1]  # 使用最后一个值

                # 将原始数据拼接到填充数据中
                processed_data = pd.concat([filled_data, processed_data], axis=0).last(req_length)

                print(f"复制填充后数据形状: {processed_data.shape}")
                print(f"新时间范围: {processed_data.index.min()} 到 {processed_data.index.max()}")

                # 添加时间特征
                if not {'hour', 'day_of_week', 'month', 'hour_sin', 'hour_cos'}.issubset(processed_data.columns):
                    self.add_time_features(processed_data)

                # 确保包含所有目标列
                for target in self.target_water_levels:
                    if target not in processed_data.columns:
                        processed_data[target] = 0
        else:
            raise TypeError("输入数据必须是Excel文件路径或DataFrame")

        # 打印数据调试信息
        print(f"处理后的数据样本:")
        print(f"索引: {processed_data.index[:3]}...")
        print(f"数据: {processed_data.shape}")
        print(processed_data.head(1).T)  # 显示第一行数据方便检查

        # 预处理数据
        try:
            # 尝试使用transform（保持预处理器状态一致）
            scaled_data = self.preprocessor.transform(processed_data)
            print("转化后数据形状：", scaled_data.shape)
        except Exception as e:
            # 如果transform失败，回退到fit_transform
            print(f"警告: transform失败 ({e})，回退到fit_transform")
            scaled_data = self.preprocessor.transform(processed_data)

        # 打印处理后的数据信息
        print(f"预处理后的数据形状: {scaled_data.shape}")

        # 创建特征序列
        X_new = None

        X_new, _, feature_names = self.preprocessor.create_sequences(scaled_data, self.sequence_length)
        print(X_new)
        print("特征列名：", feature_names)


        print(f"生成的输入序列形状: {X_new.shape}")
        return X_new, processed_data

    import os
    import pandas as pd

    def predict(self, input_data, return_original_features=True, output_file="predictions.xlsx"):
        """
        执行预测并保存结果到文件

        参数:
            input_data: 可以是以下类型:
                - Excel文件路径（包含多个sheet）
                - DataFrame（已加载的数据）
            return_original_features: 是否在结果中包含输入特征
            output_file: 保存预测结果的Excel文件路径（默认是"predictions.xlsx"）

        返回:
            预测结果DataFrame
        """
        # 准备输入数据序列
        X_new, _ = self.prepare_prediction_input(input_data)
        print("预处理后数据:", X_new.shape)

        # 进行预测
        scaled_predictions = self.model.predict(X_new)
        print("归一化预测结果示例:", scaled_predictions[0:2])

        # 反归一化预测结果
        predictions = self.preprocessor.inverse_transform_targets(scaled_predictions)
        print("预测结果示例:", predictions[0:2])

        # 创建结果DataFrame
        result_df = pd.DataFrame(
            predictions,
            columns=self.target_water_levels
        )
        self.result_df = result_df

        print("创建结果形状：", result_df.shape)
        print("创建结果示例：", result_df.head(2))

        return result_df

    def save_prediction_results(self, results_list, output_file=None, date_format='YYYY-MM-DD HH:mm:ss'):
        """保存多个时间点的预测结果到Excel - 修复保存问题"""
        if not results_list:
            print("错误：没有要保存的预测列表为空")
            return False

        try:
            # 创建完整的DataFrame - 包括所有时间点
            all_preds = []

            # 确保results_list是DataFrame列表
            if isinstance(results_list, pd.DataFrame):
                results_list = [results_list]

            for result_df in results_list:
                if not isinstance(result_df, pd.DataFrame):
                    raise TypeError(f"结果应为DataFrame, 实际类型: {type(result_df)}")

                for timestamp, row in result_df.iterrows():
                    row_dict = row.to_dict()
                    row_dict['时间'] = timestamp
                    all_preds.append(row_dict)

            # 创建结果DataFrame并重命名列
            result_df = pd.DataFrame(all_preds)
            print(f"保存结果形状: {result_df.shape}")

            # 修复列名问题 - 移除特殊字符
            renamed_columns = {}
            for col in result_df.columns:
                # 移除括号等特殊字符
                clean_col = col.replace("(", "").replace(")", "").replace(" ", "")
                renamed_columns[col] = clean_col

            result_df.rename(columns=renamed_columns, inplace=True)

            # 设置时间为索引
            if '时间' in result_df.columns:
                result_df.set_index('时间', inplace=True)
                # 格式化时间索引
                result_df.index = result_df.index.astype(str)

            # 确保目录存在
            if output_file is None:
                timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"预测结果_{timestamp_str}.xlsx"

            directory = os.path.dirname(output_file)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)

            print(f"准备保存预测结果: {len(result_df)}条记录")

            # 使用ExcelWriter设置日期格式
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 创建元数据工作表
                metadata = pd.DataFrame({
                    '预测信息': [
                        f'预测时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                        f'模型版本: LightGBM多目标预测',
                        f'节制闸数量: {len(self.gate_names)}',
                        f'预测时间步长: {len(result_df)}'
                    ]
                })
                metadata.to_excel(writer, sheet_name='预测信息', index=False)

                # 写入预测结果工作表 - 不包含index避免错误
                result_df.to_excel(writer, sheet_name='水位预测结果', index_label='时间')

                # 获取工作表并正确设置列宽
                worksheet = writer.sheets['水位预测结果']

                # 设置时间列宽
                worksheet.column_dimensions['A'].width = 20

                # 正确设置各列宽度
                max_column = len(result_df.columns) + 1
                for col_index in range(1, max_column):
                    col_letter = openpyxl.utils.get_column_letter(col_index + 1)  # +1 跳过时间列
                    worksheet.column_dimensions[col_letter].width = 20

                # 设置日期格式
                for row in worksheet.iter_rows(min_row=2, max_row=len(result_df) + 1, min_col=1, max_col=1):
                    for cell in row:
                        if cell.value:
                            cell.number_format = 'yyyy-mm-dd hh:mm'

            print(f"预测结果已成功保存到: {output_file}")
            return True

        except Exception as e:
            print(f"保存结果失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def get_user_input_for_data(hours, columns):
        """模拟获取用户输入的历史数据"""
        data = []
        print("请输入每小时的数据（按列顺序输入）：")
        for i in range(hours):
            row = []
            for column in columns:
                value = input(f"请输入 {column} 在第 {i + 1} 小时的值: ")
                row.append(float(value))
            data.append(row)
        return pd.DataFrame(data, columns=columns)

