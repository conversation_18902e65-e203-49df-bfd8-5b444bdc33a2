/* frontend/style.css */

/* --- 全局应用样式 --- */
body {
    font-family: "Helvetica Neue", Arial, sans-serif;
}

/* 设置主视图的背景颜色为一个柔和的灰色 */
div[data-testid="stAppViewContainer"] > .main {
    background-color: #f0f2f6;
}

/* 水波纹背景效果 */
div[data-testid="stAppViewContainer"] > .main::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"><rect width="100%" height="100%" fill="none" stroke="none"/><path d="M0,50 Q25,30 50,50 T100,50" stroke="%23f0f8ff" stroke-width="2" fill="none" stroke-opacity="0.3" transform="scale(1,1)"/></svg>') repeat-x;
    background-size: 100px 100px;
    opacity: 0.2;
    z-index: -1;
    animation: wave 10s linear infinite;
}

@keyframes wave {
    0% {background-position-x: 0;}
    100% {background-position-x: 100px;}
}

/* 隐藏Streamlit默认的页眉装饰线 */
div[data-testid="stHeader"] {
    background-color: rgba(255, 255, 255, 0);
}

/* 主标题美化 */
h1 {
    background: linear-gradient(90deg, #0066cc, #4d94ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
    margin-bottom: 20px;
    padding: 10px 0;
}

/* --- 侧边栏样式 --- */
/* 给侧边栏一个白色背景和右边框，使其与主区域分开 */
div[data-testid="stSidebar"] > div:first-child {
    background: linear-gradient(180deg, #ffffff, #f5f9ff);
    padding: 20px;
    border-right: 1px solid #e6e6e6;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

div[data-testid="stSidebar"] h1 {
    font-size: 1.5rem;
    margin-top: 10px;
    color: #0066cc;
}

div[data-testid="stSidebar"] .stImage {
    margin-bottom: 10px;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

div[data-testid="stSidebar"] .stImage:hover {
    transform: scale(1.05);
}

/* --- 聊天气泡样式 --- */
/* 用户聊天气泡样式 */
.user-bubble {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white; /* 白色文字 */
    float: right; /* 气泡靠右显示 */
    padding: 12px 18px;
    border-radius: 20px 20px 5px 20px; /* 自定义圆角 */
    margin: 8px 0;
    max-width: 75%;
    clear: both;
    box-shadow: 0 3px 8px rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
    line-height: 1.5;
}

.user-bubble:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* 助手聊天气泡样式 */
.assistant-bubble {
    background: linear-gradient(135deg, #ffffff, #f5f9ff);
    color: #333333; /* 深灰色文字 */
    float: left; /* 气泡靠左显示 */
    padding: 12px 18px;
    border-radius: 20px 20px 20px 5px; /* 自定义圆角 */
    margin: 8px 0;
    max-width: 75%;
    border: 1px solid #e0e0e0;
    clear: both;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    line-height: 1.5;
}

.assistant-bubble:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

/* 参考文献区域样式 */
.reference-expander {
    border-left: 4px solid #007bff;
    padding-left: 15px;
    margin-top: 12px;
    background-color: #fafafa;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.reference-expander:hover {
    background-color: #f5f9ff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.reference-expander p {
    font-size: 14px;
    margin-bottom: 8px;
    line-height: 1.5;
}

.reference-expander blockquote {
    border-left: 2px solid #e0e0e0;
    padding-left: 10px;
    color: #555;
    font-style: italic;
    margin: 10px 0;
}

/* 思考过程展开框的样式 */
.thinking-expander div[data-testid="stExpanderDetails"] {
    background-color: #f7f7f7 !important;
    border-radius: 0 0 8px 8px;
    padding: 15px;
}

/* 美化展开框 */
.streamlit-expanderHeader {
    border-radius: 10px !important;
    background-color: #f5f9ff !important;
    border-left: 4px solid #007bff !important;
    transition: all 0.3s ease !important;
    padding: 10px 15px !important;
}

.streamlit-expanderHeader:hover {
    background-color: #e6f0ff !important;
}

div[data-testid="stExpander"] {
    border: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin: 15px 0;
    border-radius: 10px !important;
    overflow: hidden;
}

/* 美化输入框 */
div[data-testid="stChatInput"] input {
    border-radius: 20px !important;
    border: 1px solid #e0e0e0 !important;
    padding: 10px 18px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
    font-size: 16px !important;
}

div[data-testid="stChatInput"] input:focus {
    border-color: #007bff !important;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.15) !important;
}

/* 美化按钮 */
button[kind="primary"] {
    background: linear-gradient(90deg, #007bff, #0056b3) !important;
    border-radius: 20px !important;
    box-shadow: 0 3px 8px rgba(0, 123, 255, 0.2) !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    padding: 8px 20px !important;
    border: none !important;
}

button[kind="primary"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3) !important;
}

/* 清除聊天记录按钮特殊样式 */
div[data-testid="stSidebar"] button[kind="primary"] {
    background: linear-gradient(90deg, #ff4d4d, #ff7875) !important;
    margin-top: 10px;
}

/* 下载按钮样式 */
.stDownloadButton button {
    background: linear-gradient(90deg, #4caf50, #45a049) !important;
    color: white !important;
    border-radius: 20px !important;
    border: none !important;
    padding: 8px 16px !important;
    box-shadow: 0 3px 5px rgba(76, 175, 80, 0.2) !important;
    transition: all 0.3s ease !important;
}

.stDownloadButton button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3) !important;
}

/* 信息提示框美化 */
div[data-testid="stAlert"] {
    border-radius: 10px !important;
    border-left-width: 4px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    padding: 15px !important;
    margin: 15px 0 !important;
}

/* 加载动画美化 */
div[data-testid="stSpinner"] {
    border-color: #007bff transparent #007bff transparent !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .user-bubble, .assistant-bubble {
        max-width: 85%;
        padding: 10px 15px;
    }
    
    h1 {
        font-size: 1.5rem !important;
    }
    
    div[data-testid="stSidebar"] {
        padding: 10px;
    }
}