# -*- coding: utf-8 -*-
import json
import time
from pathlib import Path
from typing import List, Dict
import re
import chromadb
import streamlit as st
from llama_index.core import VectorStoreIndex, StorageContext, Settings, get_response_synthesizer
from llama_index.core.schema import TextNode
from llama_index.llms.huggingface import Hugging<PERSON><PERSON>LL<PERSON>
from llama_index.embeddings.huggingface import Hugging<PERSON>aceEmbedding
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.core import PromptTemplate
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.llms.openai_like import OpenAILike


# ================== Streamlit页面配置 ==================
st.set_page_config(
    page_title="里下河网咨询助手",
    page_icon="⚖️",
    layout="centered",
    initial_sidebar_state="auto"
)

def disable_streamlit_watcher():
    """Patch Streamlit to disable file watcher"""
    def _on_script_changed(_):
        return
        
    from streamlit import runtime
    runtime.get_instance()._on_script_changed = _on_script_changed

# ================== 配置类 ==================
class Config:
    EMBED_MODEL_PATH = r"C:\Users\<USER>\PycharmProjects\pythonProject\embedding_model\sungw111\text2vec-base-chinese-sentence"
    RERANK_MODEL_PATH = r"C:\Users\<USER>\PycharmProjects\pythonProject\llms\BAAI\bge-reranker-large"  # 新增重排序模型路径

    DATA_DIR = "./data"
    VECTOR_DB_DIR = "./chroma_db"
    PERSIST_DIR = "./storage"
    
    COLLECTION_NAME = "chinese_labor_laws"
    TOP_K = 10
    RERANK_TOP_K = 3

# ================== 缓存资源初始化 ==================
@st.cache_resource(show_spinner="初始化模型中...")
def init_models():
    # 文本嵌入模型
    embed_model = HuggingFaceEmbedding(
        model_name=Config.EMBED_MODEL_PATH,
    )
    
    # 使用DeepSeek的OpenAI兼容API
    # llm = OpenAILike(
    #     model="deepseek-chat",  # 可选模型：glm-4, glm-3-turbo, characterglm等
    #     api_base="https://api.deepseek.com",  # 关键！必须指定此端点
    #     api_key="***********************************",
    #     context_window=128000,    # 按需调整（glm-4实际支持128K）
    #     is_chat_model=True,
    #     is_function_calling_model=False,  # GLM暂不支持函数调用
    #     max_tokens=1024,          # 最大生成token数（按需调整）
    #     temperature=0.3,          # 推荐范围 0.1~1.0
    #     top_p=0.7                 # 推荐范围 0.5~1.0
    # )

    # 使用阿里云的OpenAI兼容API（提供统一的接口调用llm）
    llm = OpenAILike(
        model="qwen-plus",  # 可选模型：glm-4, glm-3-turbo, characterglm等
        api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 关键！必须指定此端点
        api_key="sk-52d5a28797bc45d1b25e8673abe48510",
        context_window=128000,    # 按需调整（glm-4实际支持128K）
        is_chat_model=True,
        is_function_calling_model=False,  # GLM暂不支持函数调用
        max_tokens=1024,          # 最大生成token数（按需调整）
        temperature=0.3,          # 推荐范围 0.1~1.0
        top_p=0.7                 # 推荐范围 0.5~1.0
    )

    # 重排序模型
    reranker = SentenceTransformerRerank(
        model=Config.RERANK_MODEL_PATH,
        top_n=Config.RERANK_TOP_K
    )
    
    Settings.embed_model = embed_model
    Settings.llm = llm
    
    return embed_model, llm, reranker

# 新增：缓存NodeParser
@st.cache_resource(show_spinner="初始化文本分割器...")
def init_node_parser():
    from llama_index.core.node_parser import SentenceSplitter
    # 调整 chunk_size 和 chunk_overlap 以适应您的文档特性
    # chunk_size: 每个文本块的最大Token数
    # chunk_overlap: 文本块之间的重叠Token数，有助于保持上下文
    return SentenceSplitter(chunk_size=1024, chunk_overlap=200) #

@st.cache_resource(show_spinner="加载知识库中...")
def init_vector_store(_nodes):
    chroma_client = chromadb.PersistentClient(path=Config.VECTOR_DB_DIR)
    chroma_collection = chroma_client.get_or_create_collection(
        name=Config.COLLECTION_NAME,
        metadata={"hnsw:space": "cosine"} # 使用余弦相似度
    )

    storage_context = StorageContext.from_defaults(
        vector_store=ChromaVectorStore(chroma_collection=chroma_collection)
    )

    if chroma_collection.count() == 0 and _nodes is not None:
        storage_context.docstore.add_documents(_nodes)  
        index = VectorStoreIndex(
            _nodes,
            storage_context=storage_context,
            show_progress=True
        )
        storage_context.persist(persist_dir=Config.PERSIST_DIR)
        index.storage_context.persist(persist_dir=Config.PERSIST_DIR)
    else:
        storage_context = StorageContext.from_defaults(
            persist_dir=Config.PERSIST_DIR,
            vector_store=ChromaVectorStore(chroma_collection=chroma_collection)
        )
        index = VectorStoreIndex.from_vector_store(
            storage_context.vector_store,
            storage_context=storage_context,
            embed_model=Settings.embed_model
        )
    return index

# ================== 数据处理 ==================


# ===========数据处理json格式数据 ==========
'''
def load_and_validate_json_files(data_dir: str) -> List[Dict]:
    """加载并验证JSON法律文件"""
    json_files = list(Path(data_dir).glob("*.json"))
    assert json_files, f"未找到JSON文件于 {data_dir}"
    
    all_data = []
    for json_file in json_files:
        with open(json_file, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
                # 验证数据结构
                if not isinstance(data, list):
                    raise ValueError(f"文件 {json_file.name} 根元素应为列表")
                for item in data:
                    if not isinstance(item, dict):
                        raise ValueError(f"文件 {json_file.name} 包含非字典元素")
                    for k, v in item.items():
                        if not isinstance(v, str):
                            raise ValueError(f"文件 {json_file.name} 中键 '{k}' 的值不是字符串")
                all_data.extend({
                    "content": item,
                    "metadata": {"source": json_file.name}
                } for item in data)
            except Exception as e:
                raise RuntimeError(f"加载文件 {json_file} 失败: {str(e)}")
    
    print(f"成功加载 {len(all_data)} 个法律文件条目")
    return all_data
'''


# ===========数据处理PDF格式数据 ==========
from pypdf import PdfReader
from pathlib import Path
def load_pdfs(data_dir: str) -> List[Dict]:
    """加载 PDF 文件内容并转换为文本"""
    pdf_files = list(Path(data_dir).glob("*.pdf"))
    all_data = []

    for pdf_file in pdf_files:
        reader = PdfReader(pdf_file)
        text = ""
        for page in reader.pages:
            text += page.extract_text() or ""  # 避免 None

        all_data.append({
            "content": text,
            "metadata": {
                "source": pdf_file.name,
                "content_type": "legal_document"
            }
        })

    print(f"成功加载 {len(all_data)} 个 PDF 文件")
    return all_data
'''
def create_nodes(raw_data: List[Dict]) -> List[TextNode]:
    """添加ID稳定性保障"""
    nodes = []
    for entry in raw_data:
        law_dict = entry["content"]
        source_file = entry["metadata"]["source"]
        
        for full_title, content in law_dict.items():
            # 生成稳定ID（避免重复）
            node_id = f"{source_file}::{full_title}"
            
            parts = full_title.split(" ", 1)
            law_name = parts[0] if len(parts) > 0 else "未知法律"
            article = parts[1] if len(parts) > 1 else "未知条款"
            
            node = TextNode(
                text=content,
                id_=node_id,  # 显式设置稳定ID
                metadata={
                    "law_name": law_name,
                    "article": article,
                    "full_title": full_title,
                    "source_file": source_file,
                    "content_type": "legal_article"
                }
            )
            nodes.append(node)
    
    print(f"生成 {len(nodes)} 个文本节点（ID示例：{nodes[0].id_}）")
    return nodes
'''

# ===========专门处理PDF文本的节点生成函数 ==========
# MODIFIED: create_nodes_from_pdf 函数，接受 node_parser 参数
def create_nodes_from_pdf(raw_data: List[Dict], node_parser) -> List[TextNode]: #
    """将 PDF 文本内容转换为 TextNode 节点并进行分割"""
    all_nodes = []
    for entry in raw_data:
        content = entry["content"]
        source_file = entry["metadata"]["source"]

        # 创建一个临时的 Document 对象，NodeParser 期望处理 Document
        from llama_index.core import Document
        doc = Document(text=content, metadata={"source_file": source_file, "content_type": "legal_document"}) #

        # 使用 node_parser 分割 Document 为更小的 TextNode
        nodes = node_parser.get_nodes_from_documents([doc]) #

        # 为每个生成的节点添加元数据并确保 ID 唯一
        for i, node in enumerate(nodes):
            # 确保每个 chunk 的 ID 唯一，防止重复
            node.id_ = f"{source_file}::chunk_{i}" #
            node.metadata.update({
                "source_file": source_file,
                "content_type": "legal_document_chunk" # 区分于完整文档或 JSON 法律条文
            })
            all_nodes.append(node)

    print(f"生成 {len(all_nodes)} 个 PDF 文本节点（ID示例：{all_nodes[0].id_ if all_nodes else 'N/A'}）")
    return all_nodes

# ================== 界面组件 ==================
def init_chat_interface():
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    for msg in st.session_state.messages:
        role = msg["role"]
        content = msg.get("cleaned", msg["content"])  # 优先使用清理后的内容
        
        with st.chat_message(role):
            st.markdown(content)
            
            # 如果是助手消息且包含思维链
            if role == "assistant" and msg.get("think"):
                with st.expander("📝 模型思考过程（历史对话）"):
                    for think_content in msg["think"]:
                        st.markdown(f'<span style="color: #808080">{think_content.strip()}</span>',
                                  unsafe_allow_html=True)
            
            # 如果是助手消息且有参考依据（需要保持原有参考依据逻辑）
            if role == "assistant" and "reference_nodes" in msg:
                show_reference_details(msg["reference_nodes"])


def show_reference_details(nodes):
    with st.expander("查看支持依据"):
        for idx, node in enumerate(nodes, 1):
            meta = node.node.metadata

            # 根据 content_type 来决定显示哪些元数据
            if meta.get("content_type") == "legal_article":
                # JSON 来源的节点
                title = meta.get("full_title", "未知标题")  # 使用 .get() 避免再次 KeyError
                law_name = meta.get("law_name", "未知法律")
                article = meta.get("article", "未知条款")
                st.markdown(f"**[{idx}] {title}**")
                st.caption(f"来源文件：{meta.get('source_file', '未知文件')} | 法律名称：{law_name} | 条款：{article}")
            elif meta.get("content_type") == "legal_document_chunk":
                # PDF 来源的节点（分块后）
                # 可以使用文件名和节点ID（或手动生成的 chunk_id）来标识
                source_file = meta.get("source_file", "未知文件")
                node_id = node.node.id_  # 使用节点的唯一ID作为标识
                st.markdown(f"**[{idx}] PDF文档：{source_file}**")
                st.caption(f"内容块ID：{node_id}")
            else:
                # 其他未知类型的节点
                source_file = meta.get("source_file", "未知文件")
                st.markdown(f"**[{idx}] 未知来源：{source_file}**")
                st.caption(f"元数据：{meta}")  # 打印所有元数据以便调试

            st.markdown(f"相关度：`{node.score:.4f}`")
            st.info(f"{node.node.text}")  # 显示完整文本

# ================== 主程序 ==================
def main():
    # 禁用 Streamlit 文件热重载
    disable_streamlit_watcher()
    st.title("⚖️ 智能劳动法咨询助手")
    st.markdown("欢迎使用劳动法智能咨询系统，请输入您的问题，我们将基于最新劳动法律法规为您解答。")

    # 初始化会话状态
    if "history" not in st.session_state:
        st.session_state.history = []
    
    # 加载模型和索引
    embed_model, llm, reranker = init_models()
    node_parser = init_node_parser()

    # 初始化数据和知识库
    # 这里的条件逻辑需要特别注意，如果您修改了数据处理方式，需要强制重建索引
    # Path(Config.PERSIST_DIR).exists() 用于检查 LlamaIndex 的持久化目录
    if not Path(Config.VECTOR_DB_DIR).exists() or not Path(Config.PERSIST_DIR).exists():
        with st.spinner("正在构建知识库..."):
            pdf_data = load_pdfs(Config.DATA_DIR)
            # 将 node_parser 传递给 create_nodes_from_pdf
            pdf_nodes = create_nodes_from_pdf(pdf_data, node_parser)  #
            nodes = pdf_nodes

            # 强制删除 ChromaDB collection 以重建索引（重要！）
            # 如果您更改了任何数据处理逻辑（特别是分块），必须删除旧的 Collection
            chroma_client = chromadb.PersistentClient(path=Config.VECTOR_DB_DIR)
            try:
                chroma_client.delete_collection(name=Config.COLLECTION_NAME)
                print(f"Collection '{Config.COLLECTION_NAME}' deleted for rebuild.")
            except Exception as e:
                print(f"Could not delete collection (might not exist yet): {e}")

    else:
        nodes = None  # 如果持久化目录存在，则从磁盘加载，无需再次生成节点
    
    index = init_vector_store(nodes)
    retriever = index.as_retriever(similarity_top_k=Config.TOP_K,vector_store_query_mode="hybrid",alpha=0.5)
    # 调整检索器配置（扩大召回范围并启用混合检索）
    # retriever = index.as_retriever(
    #     similarity_top_k=20,  # 从10提升到20
    #     vector_store_query_mode="hybrid",  # 混合检索模式
    #     alpha=0.5,  # 平衡密集检索与稀疏检索
    #     filters={"content_type": "legal_article"}  # 添加元数据过滤
    # )
    response_synthesizer = get_response_synthesizer(verbose=True)
    
    # 聊天界面
    init_chat_interface()
    
    if prompt := st.chat_input("请输入劳动法相关问题"):
        # 添加用户消息到历史
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # 处理查询
        with st.spinner("正在分析问题..."):
            start_time = time.time()
            
            # 检索流程
            initial_nodes = retriever.retrieve(prompt)
            reranked_nodes = reranker.postprocess_nodes(initial_nodes, query_str=prompt)
            
            # 过滤节点
            MIN_RERANK_SCORE = 0.4
            filtered_nodes = [node for node in reranked_nodes if node.score > MIN_RERANK_SCORE]
            
            if not filtered_nodes:
                response_text = "⚠️ 未找到相关法律条文，请尝试调整问题描述或咨询专业律师。"
            else:
                # 生成回答
                response = response_synthesizer.synthesize(prompt, nodes=filtered_nodes)
                response_text = response.response
            
            # 显示回答
            with st.chat_message("assistant"):
                # 提取思维链内容并清理响应文本
                think_contents = re.findall(r'<think>(.*?)</think>', response_text, re.DOTALL)
                cleaned_response = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL).strip()
                
                # 显示清理后的回答
                st.markdown(cleaned_response)
                
                # 如果有思维链内容则显示
                if think_contents:
                    with st.expander("📝 模型思考过程（点击展开）"):
                        for content in think_contents:
                            st.markdown(f'<span style="color: #808080">{content.strip()}</span>', 
                                      unsafe_allow_html=True)
                
                # 显示参考依据（保持原有逻辑）
                show_reference_details(filtered_nodes[:3])

            # 添加助手消息到历史（需要存储原始响应）
            st.session_state.messages.append({
                "role": "assistant",
                "content": response_text,  # 保留原始响应
                "cleaned": cleaned_response,  # 存储清理后的文本
                "think": think_contents  # 存储思维链内容
            })

if __name__ == "__main__":
    main()