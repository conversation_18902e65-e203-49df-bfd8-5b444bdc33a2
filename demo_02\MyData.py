from torch.utils.data import Dataset
from datasets import load_from_disk

# Dataset是 PyTorch 中用于定义数据集的基类，继承它可以创建自定义数据集。
# 继承Dataset这样就可以使用 PyTorch 的数据加载机制来处理这个数据集。
class MyDataset(Dataset):
    #初始化数据集
    def __init__(self,split):
        #从磁盘加载数据
        self.dataset = load_from_disk(r"C:\Users\<USER>\PycharmProjects\pythonProject\demo_02\data\ChnSentiCorp")
        if split == "train":
            self.dataset = self.dataset["train"]
        elif split == "test":
            self.dataset = self.dataset["test"]
        elif split == "validation":
            self.dataset = self.dataset["validation"]
        else:
            print("数据名错误！")

    #返回数据集长度
    def __len__(self):
        return len(self.dataset)

    #对每条数据单独做处理
    def __getitem__(self, item):
        text = self.dataset[item]["text"]
        label = self.dataset[item]["label"]

        return text,label

if __name__ == '__main__':
    dataset = MyDataset("train")
    for data in dataset:
        print(data)