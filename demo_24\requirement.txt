accelerate==1.6.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
altair==5.5.0
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
attrs==25.3.0
backoff==2.2.1
banks==2.1.2
bcrypt==4.3.0
beautifulsoup4==4.13.4
blinker==1.9.0
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.4.26
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==1.0.7
click==8.1.8
colorama==0.4.6
coloredlogs==15.0.1
dataclasses-json==0.6.7
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
durationpy==0.9
fastapi==0.115.9
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
frozenlist==1.6.0
fsspec==2025.3.2
gitdb==4.0.12
GitPython==3.1.44
google-auth==2.39.0
googleapis-common-protos==1.70.0
greenlet==3.2.1
griffe==1.7.3
grpcio==1.71.0
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.30.2
humanfriendly==10.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
jieba==0.42.1
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
kubernetes==32.0.1
llama-cloud==0.1.19
llama-cloud-services==0.6.20
llama-index==0.12.33
llama-index-agent-openai==0.4.6
llama-index-cli==0.4.1
llama-index-core==0.12.33.post1
llama-index-embeddings-huggingface==0.5.3
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.11
llama-index-llms-huggingface==0.5.0
llama-index-llms-openai==0.3.38
llama-index-llms-openai-like==0.3.4
llama-index-multi-modal-llms-openai==0.4.3
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.7
llama-index-readers-llama-parse==0.4.0
llama-index-vector-stores-chroma==0.4.1
llama-parse==0.6.20
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
mdurl==0.1.2
mmh3==5.1.0
modelscope==1.25.0
mpmath==1.3.0
multidict==6.4.3
mypy_extensions==1.1.0
narwhals==1.37.1
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.5
nvidia-cublas-cu12==12.6.4.1
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==9.5.1.17
nvidia-cufft-cu12==11.3.0.4
nvidia-cufile-cu12==1.11.1.6
nvidia-curand-cu12==10.3.7.77
nvidia-cusolver-cu12==11.7.1.2
nvidia-cusparse-cu12==12.5.4.2
nvidia-cusparselt-cu12==0.6.3
nvidia-ml-py==12.570.86
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
nvitop==1.4.2
oauthlib==3.2.2
onnxruntime==1.21.1
openai==1.74.0
opentelemetry-api==1.32.1
opentelemetry-exporter-otlp-proto-common==1.32.1
opentelemetry-exporter-otlp-proto-grpc==1.32.1
opentelemetry-instrumentation==0.53b1
opentelemetry-instrumentation-asgi==0.53b1
opentelemetry-instrumentation-fastapi==0.53b1
opentelemetry-proto==1.32.1
opentelemetry-sdk==1.32.1
opentelemetry-semantic-conventions==0.53b1
opentelemetry-util-http==0.53b1
orjson==3.10.18
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pillow==11.2.1
pip==25.0
platformdirs==4.3.7
posthog==4.0.1
propcache==0.3.1
protobuf==5.29.4
psutil==7.0.0
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic==2.11.4
pydantic_core==2.33.2
pydeck==0.9.1
Pygments==2.19.1
pypdf==5.4.0
PyPika==0.48.9
pyproject_hooks==1.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
PyYAML==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
rich==14.0.0
rpds-py==0.24.0
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==4.1.0
setuptools==75.8.0
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.40
starlette==0.45.3
streamlit==1.45.0
striprtf==0.0.26
sympy==1.14.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
toml==0.10.2
torch==2.7.0
tornado==6.4.2
tqdm==4.67.1
transformers==4.51.3
triton==3.3.0
typer==0.15.3
typing_extensions==4.13.2
typing-inspect==0.9.0
typing-inspection==0.4.0
tzdata==2025.2
urllib3==2.4.0
uvicorn==0.34.2
uvloop==0.21.0
watchdog==6.0.0
watchfiles==1.0.5
websocket-client==1.8.0
websockets==15.0.1
wheel==0.45.1
wrapt==1.17.2
yarl==1.20.0
zipp==3.21.0
