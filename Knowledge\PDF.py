from pypdf import PdfReader
from pathlib import Path

def load_pdfs(data_dir: str) -> List[Dict]:
    pdf_files = list(Path(data_dir).glob("*.pdf"))
    all_data = []

    for pdf_file in pdf_files:
        reader = PdfReader(pdf_file)
        text = ""
        for page in reader.pages:
            text += page.extract_text()

        # 将整篇 PDF 内容作为一个条目（也可以按页分割）
        all_data.append({
            "content": text,
            "metadata": {
                "source": pdf_file.name,
                "content_type": "legal_document"
            }
        })

    print(f"成功加载 {len(all_data)} 个 PDF 文件")
    return all_data
